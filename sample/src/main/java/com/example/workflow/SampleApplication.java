package com.example.workflow;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 工作流SDK集成示例应用
 */
@SpringBootApplication(scanBasePackages = {
    "com.example.workflow",           // 扫描示例代码
    "com.stationdm.workflow"          // 扫描SDK组件
})
public class SampleApplication {

    public static void main(String[] args) {
        SpringApplication.run(SampleApplication.class, args);
        System.out.println("\n=== 工作流SDK示例应用已启动 ===");
        System.out.println("访问: http://localhost:8080/api/workflow/run?input=查询天气");
        System.out.println("文档: http://localhost:8080/api/workflow/help");
        System.out.println("================================\n");
    }
}
