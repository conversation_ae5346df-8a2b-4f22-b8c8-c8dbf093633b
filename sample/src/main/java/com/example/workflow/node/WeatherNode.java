package com.example.workflow.node;

import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 天气查询节点
 * 演示如何创建支持用户交互的自定义节点
 */
@Slf4j
@Getter
@Setter
public class WeatherNode extends WorkflowNode {
    
    private String city;
    private String date;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        log.info("开始执行天气查询节点 - ID: {}", getId());
        context.emitStreamingEvent("NODE_MESSAGE", getId(), "开始天气查询");
        
        return validateAndQuery(context);
    }
    
    /**
     * 验证参数并查询天气
     * 如果参数不完整，会请求用户输入
     */
    private Mono<Void> validateAndQuery(WorkflowContext context) {
        // 检查城市参数
        if (city == null || city.trim().isEmpty()) {
            log.info("城市参数缺失，请求用户输入 - NodeID: {}", getId());
            context.emitStreamingEvent("NODE_MESSAGE", getId(), "城市参数缺失，请求用户输入...");
            
            return requestUserInput("请提供需要查询天气的城市名称：", context)
                .doOnNext(userCity -> {
                    this.city = userCity.trim();
                    log.info("用户输入城市: {} - NodeID: {}", this.city, getId());
                    context.emitStreamingEvent("NODE_MESSAGE", getId(), "用户输入城市: " + this.city);
                })
                .then(Mono.defer(() -> validateAndQuery(context)));
        }
        
        // 检查日期参数
        if (date == null || date.trim().isEmpty()) {
            log.info("日期参数缺失，请求用户输入 - NodeID: {}", getId());
            context.emitStreamingEvent("NODE_MESSAGE", getId(), "日期参数缺失，请求用户输入...");
            
            return requestUserInput("请提供需要查询天气的日期 (例如 2024-01-15):", context)
                .doOnNext(userDate -> {
                    this.date = userDate.trim();
                    log.info("用户输入日期: {} - NodeID: {}", this.date, getId());
                    context.emitStreamingEvent("NODE_MESSAGE", getId(), "用户输入日期: " + this.date);
                })
                .then(Mono.defer(() -> validateAndQuery(context)));
        }
        
        // 参数完整，执行查询
        return executeWeatherQuery(context);
    }
    
    /**
     * 执行天气查询
     */
    private Mono<Void> executeWeatherQuery(WorkflowContext context) {
        log.info("执行天气查询 - City: {}, Date: {}, NodeID: {}", city, date, getId());
        context.emitStreamingEvent("NODE_MESSAGE", getId(), "正在查询天气信息...");
        
        return Mono.fromCallable(() -> {
            // 模拟天气查询
            String weatherInfo = String.format("在 %s, %s 的天气是：晴朗，温度 25℃，湿度 60%%，风力3级。", city, date);
            
            // 保存结果到上下文
            context.addNodeResult(getId() + "_result", weatherInfo);
            context.addNodeResult("weatherInfo", weatherInfo);
            
            log.info("天气查询完成 - Result: {}, NodeID: {}", weatherInfo, getId());
            context.emitStreamingEvent("NODE_MESSAGE", getId(), "天气查询完成: " + weatherInfo);
            
            // 发送结构化结果事件
            context.emitStreamingEvent("WEATHER_QUERY_COMPLETED", getId(), 
                java.util.Map.of(
                    "city", city,
                    "date", date,
                    "weather", weatherInfo,
                    "nodeId", getId()
                ));
            
            return null;
        })
        .onErrorResume(error -> {
            String errorMessage = "天气查询失败: " + error.getMessage();
            log.error("天气查询失败 - NodeID: {}", getId(), error);
            context.emitStreamingEvent("NODE_ERROR", getId(), errorMessage);
            return Mono.error(error);
        })
        .then();
    }
}
