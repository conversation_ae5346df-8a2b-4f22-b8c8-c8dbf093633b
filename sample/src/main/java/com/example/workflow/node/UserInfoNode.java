package com.example.workflow.node;

import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 用户信息收集节点
 * 演示如何收集多个用户输入
 */
@Slf4j
@Getter
@Setter
public class UserInfoNode extends WorkflowNode {
    
    private String userName;
    private String userEmail;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        log.info("开始执行用户信息收集节点 - ID: {}", getId());
        context.emitStreamingEvent("NODE_MESSAGE", getId(), "开始收集用户信息");
        
        return collectUserInfo(context);
    }
    
    /**
     * 收集用户信息
     */
    private Mono<Void> collectUserInfo(WorkflowContext context) {
        // 检查用户名
        if (userName == null || userName.trim().isEmpty()) {
            log.info("用户名缺失，请求用户输入 - NodeID: {}", getId());
            context.emitStreamingEvent("NODE_MESSAGE", getId(), "请输入您的姓名");
            
            return requestUserInput("请输入您的姓名：", context)
                .doOnNext(name -> {
                    this.userName = name.trim();
                    log.info("用户输入姓名: {} - NodeID: {}", this.userName, getId());
                    context.emitStreamingEvent("NODE_MESSAGE", getId(), "收到姓名: " + this.userName);
                })
                .then(Mono.defer(() -> collectUserInfo(context)));
        }
        
        // 检查邮箱
        if (userEmail == null || userEmail.trim().isEmpty()) {
            log.info("邮箱缺失，请求用户输入 - NodeID: {}", getId());
            context.emitStreamingEvent("NODE_MESSAGE", getId(), "请输入您的邮箱");
            
            return requestUserInput("请输入您的邮箱地址：", context)
                .doOnNext(email -> {
                    this.userEmail = email.trim();
                    log.info("用户输入邮箱: {} - NodeID: {}", this.userEmail, getId());
                    context.emitStreamingEvent("NODE_MESSAGE", getId(), "收到邮箱: " + this.userEmail);
                })
                .then(Mono.defer(() -> collectUserInfo(context)));
        }
        
        // 信息收集完成
        return completeUserInfo(context);
    }
    
    /**
     * 完成用户信息收集
     */
    private Mono<Void> completeUserInfo(WorkflowContext context) {
        String userInfo = String.format("用户信息 - 姓名: %s, 邮箱: %s", userName, userEmail);
        
        // 保存到上下文
        context.addNodeResult(getId() + "_result", userInfo);
        context.addNodeResult("userName", userName);
        context.addNodeResult("userEmail", userEmail);
        
        log.info("用户信息收集完成 - {}, NodeID: {}", userInfo, getId());
        context.emitStreamingEvent("NODE_MESSAGE", getId(), "用户信息收集完成: " + userInfo);
        
        // 发送完成事件
        context.emitStreamingEvent("USER_INFO_COLLECTED", getId(), 
            java.util.Map.of(
                "userName", userName,
                "userEmail", userEmail,
                "nodeId", getId()
            ));
        
        return Mono.empty();
    }
}
