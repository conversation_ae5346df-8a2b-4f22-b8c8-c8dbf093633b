package com.example.workflow.config;

import com.example.workflow.node.UserInfoNode;
import com.example.workflow.node.WeatherNode;
import com.stationdm.workflow.registry.NodeTypeRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

/**
 * 工作流配置
 * 注册自定义节点类型
 */
@Slf4j
@Configuration
public class WorkflowConfig {

    @Autowired
    private NodeTypeRegistry nodeTypeRegistry;

    /**
     * 应用启动后注册自定义节点类型
     */
    @EventListener(ApplicationReadyEvent.class)
    public void registerCustomNodes() {
        log.info("开始注册自定义节点类型...");
        
        try {
            // 注册天气查询节点
            nodeTypeRegistry.registerNodeType("WEATHER", WeatherNode.class);
            log.info("注册节点类型: WEATHER -> {}", WeatherNode.class.getName());
            
            // 注册用户信息节点
            nodeTypeRegistry.registerNodeType("USER_INFO", UserInfoNode.class);
            log.info("注册节点类型: USER_INFO -> {}", UserInfoNode.class.getName());
            
            log.info("自定义节点类型注册完成");
            
        } catch (Exception e) {
            log.error("注册自定义节点类型失败", e);
            throw new RuntimeException("Failed to register custom node types", e);
        }
    }
}
