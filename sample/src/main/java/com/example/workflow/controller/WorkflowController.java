package com.example.workflow.controller;

import com.example.workflow.handler.ChatHandler;
import com.stationdm.workflow.engine.WorkflowBuilder;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.streaming.StreamingEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * 工作流控制器
 * 演示如何集成工作流SDK并处理用户交互
 */
@Slf4j
@RestController
@RequestMapping("/api/workflow")
public class WorkflowController {

    @Autowired
    private WorkflowBuilder workflowBuilder;
    
    @Autowired
    private ChatHandler chatHandler;
    
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 运行工作流 - 支持流式响应和用户交互
     */
    @GetMapping(value = "/run", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> runWorkflow(@RequestParam String input) {
        String traceId = UUID.randomUUID().toString();
        log.info("启动工作流 - TraceID: {}, Input: {}", traceId, input);
        
        return Flux.<ServerSentEvent<String>>create(sink -> {
            try {
                // 创建工作流上下文
                WorkflowContext context = new WorkflowContext(applicationContext);
                context.addNodeResult("userInput", input);
                context.addNodeResult("traceId", traceId);
                
                // 监听工作流事件流
                context.getEventStream().subscribe(
                    event -> handleStreamingEvent(event, sink, traceId),
                    error -> {
                        log.error("工作流事件流错误 - TraceID: {}", traceId, error);
                        sink.next(sse("错误: " + error.getMessage()));
                        sink.error(error);
                    },
                    () -> {
                        log.info("工作流事件流完成 - TraceID: {}", traceId);
                        sink.next(sse("工作流执行完成"));
                        sink.complete();
                    }
                );
                
                // 加载并执行工作流
                String workflowJson = loadWorkflowJson();
                workflowBuilder.build(workflowJson).execute(context)
                    .subscribe(
                        v -> {
                            log.info("工作流执行成功 - TraceID: {}", traceId);
                            context.completeEventStream();
                        },
                        error -> {
                            log.error("工作流执行失败 - TraceID: {}", traceId, error);
                            sink.next(sse("工作流执行失败: " + error.getMessage()));
                            context.completeEventStream();
                        }
                    );
                    
            } catch (Exception e) {
                log.error("工作流启动失败 - TraceID: {}", traceId, e);
                sink.next(sse("工作流启动失败: " + e.getMessage()));
                sink.error(e);
            }
        })
        .mergeWith(Flux.interval(java.time.Duration.ofSeconds(30))
            .map(i -> sse("keep-alive")))
        .onErrorResume(e -> {
            log.error("SSE流异常", e);
            return Flux.just(sse("连接异常: " + e.getMessage()));
        });
    }

    /**
     * 提交用户输入
     */
    @PostMapping("/submit")
    public Mono<String> submitUserInput(
            @RequestParam String interactionId, 
            @RequestParam String answer) {
        
        log.info("收到用户输入提交 - InteractionID: {}, Answer: {}", interactionId, answer);
        
        boolean success = chatHandler.submitUserInput(interactionId, answer);
        
        if (success) {
            return Mono.just("提交成功");
        } else {
            return Mono.just("提交失败：未找到对应的交互请求");
        }
    }

    /**
     * 获取待处理的交互列表
     */
    @GetMapping("/pending")
    public Mono<Object> getPendingInteractions() {
        return Mono.just(java.util.Map.of(
            "pendingInteractions", chatHandler.getPendingInteractionIds(),
            "count", chatHandler.getPendingInteractionIds().size()
        ));
    }

    /**
     * 取消交互
     */
    @PostMapping("/cancel")
    public Mono<String> cancelInteraction(
            @RequestParam String interactionId,
            @RequestParam(defaultValue = "用户取消") String reason) {
        
        chatHandler.cancelInteraction(interactionId, reason);
        return Mono.just("交互已取消");
    }

    /**
     * 帮助信息
     */
    @GetMapping("/help")
    public Mono<String> help() {
        return Mono.just("""
            工作流SDK集成示例
            
            API接口:
            1. GET  /api/workflow/run?input=查询天气    - 启动工作流
            2. POST /api/workflow/submit               - 提交用户输入
            3. GET  /api/workflow/pending              - 查看待处理交互
            4. POST /api/workflow/cancel               - 取消交互
            
            使用示例:
            1. 启动工作流: curl "http://localhost:8080/api/workflow/run?input=查询天气"
            2. 提交输入: curl -X POST "http://localhost:8080/api/workflow/submit" -d "interactionId=xxx&answer=北京"
            """);
    }

    /**
     * 处理流式事件
     */
    private void handleStreamingEvent(StreamingEvent event, 
                                    reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink,
                                    String traceId) {
        String eventType = event.getEventType();
        Object data = event.getData();
        String nodeId = event.getNodeId();
        
        log.debug("处理流式事件 - TraceID: {}, EventType: {}, NodeID: {}", traceId, eventType, nodeId);
        
        switch (eventType) {
            case "REQUEST_USER_INPUT" -> {
                // 生成交互ID
                String interactionId = traceId + "_" + nodeId;
                String question = data.toString();
                
                // 发送结构化提问格式（兼容前端）
                String structuredQuestion = String.format("提问::%s::%s", interactionId, question);
                sink.next(sse(structuredQuestion));
                
                log.info("发送用户交互请求 - TraceID: {}, InteractionID: {}, Question: {}", 
                    traceId, interactionId, question);
            }
            case "NODE_MESSAGE" -> {
                sink.next(sse("节点消息: " + data));
            }
            case "NODE_ERROR" -> {
                sink.next(sse("节点错误: " + data));
            }
            default -> {
                sink.next(sse(eventType + ": " + data));
            }
        }
    }

    /**
     * 创建SSE事件
     */
    private ServerSentEvent<String> sse(String data) {
        return ServerSentEvent.<String>builder()
            .data(data)
            .build();
    }

    /**
     * 加载工作流JSON配置
     */
    private String loadWorkflowJson() throws Exception {
        ClassPathResource resource = new ClassPathResource("workflows/sample-workflow.json");
        return resource.getContentAsString(StandardCharsets.UTF_8);
    }
}
