package com.example.workflow.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用户交互处理器
 * 基于Sink实现用户输入等待机制
 */
@Slf4j
@Component
public class ChatHandler {

    // 存储待处理的用户输入请求
    private final Map<String, Sinks.One<String>> pendingInputs = new ConcurrentHashMap<>();

    /**
     * 等待用户输入
     * 
     * @param interactionId 交互ID
     * @param question 问题内容
     * @param timeoutMinutes 超时时间（分钟）
     * @return 用户输入的内容
     */
    public Mono<String> waitForUserInput(String interactionId, String question, int timeoutMinutes) {
        log.info("等待用户输入 - InteractionID: {}, Question: {}", interactionId, question);
        
        Sinks.One<String> sink = Sinks.one();
        pendingInputs.put(interactionId, sink);
        
        return sink.asMono()
            .timeout(Duration.ofMinutes(timeoutMinutes))
            .doOnSuccess(response -> {
                log.info("收到用户输入 - InteractionID: {}, Answer: {}", interactionId, response);
            })
            .doOnError(error -> {
                log.warn("用户输入失败 - InteractionID: {}, Error: {}", interactionId, error.getMessage());
            })
            .doFinally(signal -> {
                pendingInputs.remove(interactionId);
                log.debug("清理交互资源 - InteractionID: {}", interactionId);
            });
    }

    /**
     * 提交用户输入
     * 
     * @param interactionId 交互ID
     * @param userInput 用户输入
     * @return 是否成功提交
     */
    public boolean submitUserInput(String interactionId, String userInput) {
        log.info("提交用户输入 - InteractionID: {}, Input: {}", interactionId, userInput);
        
        Sinks.One<String> sink = pendingInputs.get(interactionId);
        if (sink != null) {
            Sinks.EmitResult result = sink.tryEmitValue(userInput);
            if (result.isSuccess()) {
                log.info("用户输入提交成功 - InteractionID: {}", interactionId);
                return true;
            } else {
                log.warn("用户输入提交失败 - InteractionID: {}, Result: {}", interactionId, result);
                pendingInputs.remove(interactionId);
                return false;
            }
        } else {
            log.warn("未找到待处理的交互 - InteractionID: {}", interactionId);
            return false;
        }
    }

    /**
     * 获取待处理的交互ID列表
     * 
     * @return 交互ID集合
     */
    public java.util.Set<String> getPendingInteractionIds() {
        return new java.util.HashSet<>(pendingInputs.keySet());
    }

    /**
     * 取消交互
     * 
     * @param interactionId 交互ID
     * @param reason 取消原因
     */
    public void cancelInteraction(String interactionId, String reason) {
        Sinks.One<String> sink = pendingInputs.get(interactionId);
        if (sink != null) {
            sink.tryEmitError(new RuntimeException("交互已取消: " + reason));
            pendingInputs.remove(interactionId);
            log.info("交互已取消 - InteractionID: {}, Reason: {}", interactionId, reason);
        }
    }
}
