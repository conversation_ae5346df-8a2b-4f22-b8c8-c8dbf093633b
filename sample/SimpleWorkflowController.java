package com.example.workflow.controller;

import com.stationdm.workflow.engine.WorkflowEngineService;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.interaction.InteractionManager;
import com.stationdm.workflow.streaming.StreamingEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * 简单的工作流控制器
 * 基于您现有的 WorkflowEngineService
 */
@Slf4j
@RestController
@RequestMapping("/api/workflow")
public class SimpleWorkflowController {

    @Autowired
    private WorkflowEngineService workflowEngineService;
    
    @Autowired
    private InteractionManager interactionManager;
    
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 运行工作流 - 支持用户交互和流式响应
     */
    @GetMapping(value = "/run", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> runWorkflow(@RequestParam String input) {
        String traceId = UUID.randomUUID().toString();
        log.info("启动工作流 - TraceID: {}, Input: {}", traceId, input);
        
        return Flux.<ServerSentEvent<String>>create(sink -> {
            // 创建工作流上下文
            WorkflowContext context = new WorkflowContext(applicationContext);
            context.addNodeResult("userInput", input);
            context.addNodeResult("traceId", traceId);
            
            // 监听工作流事件流
            context.getEventStream().subscribe(
                event -> {
                    // 处理交互请求事件
                    if ("REQUEST_USER_INPUT".equals(event.getEventType())) {
                        String interactionId = traceId + "_" + event.getNodeId();
                        String question = event.getData().toString();
                        
                        // 发送结构化提问格式
                        String structuredQuestion = String.format("提问::%s::%s", interactionId, question);
                        sink.next(ServerSentEvent.<String>builder().data(structuredQuestion).build());
                        
                        log.info("发送交互请求 - InteractionID: {}, Question: {}", interactionId, question);
                    } else {
                        // 其他事件直接转发
                        sink.next(ServerSentEvent.<String>builder().data(event.getEventType() + ": " + event.getData()).build());
                    }
                },
                error -> {
                    log.error("事件流错误", error);
                    sink.next(ServerSentEvent.<String>builder().data("错误: " + error.getMessage()).build());
                    sink.error(error);
                },
                () -> {
                    log.info("工作流完成");
                    sink.next(ServerSentEvent.<String>builder().data("工作流执行完成").build());
                    sink.complete();
                }
            );
            
            // 使用 WorkflowEngineService 执行工作流
            workflowEngineService.executeWorkflow("your-workflow-name", context)
                .subscribe(
                    v -> {
                        log.info("工作流执行成功");
                        context.completeEventStream();
                    },
                    error -> {
                        log.error("工作流执行失败", error);
                        sink.next(ServerSentEvent.<String>builder().data("执行失败: " + error.getMessage()).build());
                        context.completeEventStream();
                    }
                );
        })
        .mergeWith(Flux.interval(java.time.Duration.ofSeconds(30))
            .map(i -> ServerSentEvent.<String>builder().data("keep-alive").build()));
    }

    /**
     * 提交用户输入
     */
    @PostMapping("/submit")
    public Mono<String> submitUserInput(
            @RequestParam String interactionId, 
            @RequestParam String answer) {
        
        log.info("收到用户输入 - InteractionID: {}, Answer: {}", interactionId, answer);
        
        boolean success = interactionManager.submitUserInput(interactionId, answer);
        return Mono.just(success ? "提交成功" : "提交失败");
    }

    /**
     * 获取待处理的交互
     */
    @GetMapping("/pending")
    public Mono<Object> getPendingInteractions() {
        return Mono.just(interactionManager.getPendingInteractionIds());
    }
}
