# 工作流SDK集成示例

这个示例展示了如何集成和使用工作流SDK，包括用户交互功能。

## 🚀 快速开始

### 1. 构建SDK
```bash
# 在SDK项目根目录执行
mvn clean install
```

### 2. 运行示例
```bash
# 进入示例目录
cd sample

# 启动示例应用
mvn spring-boot:run
```

### 3. 测试交互功能
```bash
# 启动工作流（会在参数缺失时请求用户输入）
curl "http://localhost:8080/api/workflow/run?input=查询天气"

# 在另一个终端提交用户输入
curl -X POST "http://localhost:8080/api/workflow/submit" \
  -d "interactionId=your_interaction_id&answer=北京"
```

## 📁 项目结构

```
sample/
├── pom.xml                          # Maven配置
├── src/main/java/
│   └── com/example/workflow/
│       ├── SampleApplication.java   # Spring Boot启动类
│       ├── controller/
│       │   └── WorkflowController.java  # 工作流控制器
│       ├── handler/
│       │   └── ChatHandler.java     # 用户交互处理器
│       ├── node/
│       │   ├── WeatherNode.java     # 示例天气节点
│       │   └── UserInfoNode.java    # 示例用户信息节点
│       └── config/
│           └── WorkflowConfig.java  # 工作流配置
└── src/main/resources/
    ├── application.yml              # 应用配置
    └── workflows/
        └── sample-workflow.json     # 示例工作流定义
```

## 🎯 核心功能

1. **用户交互** - 节点可以在参数缺失时请求用户输入
2. **流式响应** - 实时推送工作流执行状态
3. **自定义节点** - 展示如何创建自定义业务节点
4. **错误处理** - 完整的异常处理机制

## 📖 详细说明

查看各个文件了解具体实现细节：
- `WorkflowController.java` - 如何处理SSE和用户输入
- `ChatHandler.java` - 如何实现用户交互处理
- `WeatherNode.java` - 如何创建支持交互的自定义节点
- `sample-workflow.json` - 如何定义工作流
