# 工作流SDK简单使用指南

## 🚀 快速集成

### 1. 添加依赖
```xml
<dependency>
    <groupId>com.stationdm.workflow</groupId>
    <artifactId>kintaro2-workflow</artifactId>
    <version>1.0.0.0</version>
</dependency>
```

### 2. 复制控制器
将 `SimpleWorkflowController.java` 复制到您的项目中

### 3. 实现 InteractionManager
```java
@Component
public class ChatHandler {
    private final Map<String, Sinks.One<String>> pendingInputs = new ConcurrentHashMap<>();
    
    public Mono<String> waitForUserInput(String interactionId, String question, int timeoutMinutes) {
        Sinks.One<String> sink = Sinks.one();
        pendingInputs.put(interactionId, sink);
        return sink.asMono().timeout(Duration.ofMinutes(timeoutMinutes));
    }
    
    public boolean submitUserInput(String interactionId, String userInput) {
        Sinks.One<String> sink = pendingInputs.get(interactionId);
        if (sink != null) {
            return sink.tryEmitValue(userInput).isSuccess();
        }
        return false;
    }
}
```

## 🎯 使用方式

### 启动工作流
```bash
curl "http://localhost:8080/api/workflow/run?input=查询天气"
```

### 提交用户输入
```bash
curl -X POST "http://localhost:8080/api/workflow/submit" \
  -d "interactionId=traceId_nodeId&answer=北京"
```

## 📝 在节点中使用交互

```java
public class MyNode extends WorkflowNode {
    private String city;
    
    @Override
    public Mono<Void> execute(WorkflowContext context) {
        if (city == null) {
            return requestUserInput("请输入城市:", context)
                .doOnNext(input -> this.city = input)
                .then(Mono.defer(() -> execute(context)));
        }
        
        // 执行业务逻辑
        return doWork(context);
    }
}
```

## 🔄 前端处理

```javascript
const eventSource = new EventSource('/api/workflow/run?input=查询天气');

eventSource.onmessage = function(event) {
    const data = event.data;
    
    if (data.startsWith("提问::")) {
        const [_, interactionId, question] = data.split("::");
        
        // 显示问题给用户
        const answer = prompt(question);
        
        // 提交答案
        fetch('/api/workflow/submit', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: `interactionId=${interactionId}&answer=${answer}`
        });
    } else {
        console.log('工作流消息:', data);
    }
};
```

就这么简单！
