<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能聊天演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.assistant {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 4px;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .message.user .message-avatar {
            background: #007bff;
            order: 1;
        }

        .message.assistant .message-avatar {
            background: #28a745;
        }

        .chat-input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #007bff;
        }

        .send-button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .send-button:hover:not(:disabled) {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .status-indicator {
            padding: 8px 16px;
            margin: 10px 0;
            border-radius: 20px;
            font-size: 14px;
            text-align: center;
        }

        .status-indicator.connecting {
            background: #fff3cd;
            color: #856404;
        }

        .status-indicator.connected {
            background: #d4edda;
            color: #155724;
        }

        .status-indicator.error {
            background: #f8d7da;
            color: #721c24;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            margin: 10px 0;
        }

        .typing-indicator.show {
            display: flex;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
            margin-left: 10px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #007bff;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .question-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .question-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
        }

        .question-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .question-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .question-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🤖 智能聊天助手
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    您好！我是您的智能助手，很高兴为您服务。请告诉我您需要什么帮助？
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <div class="message-avatar" style="background: #28a745;">AI</div>
            <span>正在输入</span>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
        
        <div class="status-indicator" id="statusIndicator"></div>
        
        <div class="chat-input-area">
            <div class="input-group">
                <input type="text" class="chat-input" id="chatInput" 
                       placeholder="输入您的问题..." maxlength="1000">
                <button class="send-button" id="sendButton" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <!-- 问题弹窗 -->
    <div class="question-modal" id="questionModal">
        <div class="question-content">
            <div class="question-title" id="questionTitle">系统提问</div>
            <input type="text" class="question-input" id="questionInput" placeholder="请输入您的回答...">
            <div class="question-buttons">
                <button class="btn btn-secondary" onclick="cancelQuestion()">取消</button>
                <button class="btn btn-primary" onclick="submitAnswer()">提交</button>
            </div>
        </div>
    </div>

    <script>
        let currentEventSource = null;
        let currentInteractionId = null;
        let isConnected = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 显示用户消息
            addMessage('user', message);
            
            // 清空输入框
            input.value = '';
            
            // 禁用发送按钮
            toggleSendButton(false);
            
            // 显示状态
            showStatus('connecting', '正在连接...');
            
            // 显示输入指示器
            showTypingIndicator(true);
            
            // 启动聊天流
            startChatStream(message);
        }

        // 启动聊天流
        function startChatStream(message) {
            // 关闭之前的连接
            if (currentEventSource) {
                currentEventSource.close();
            }
            
            const url = `/api/chat/stream?input=${encodeURIComponent(message)}`;
            currentEventSource = new EventSource(url);
            
            let assistantMessage = '';
            let messageElement = null;
            
            currentEventSource.onopen = function() {
                isConnected = true;
                showStatus('connected', '已连接');
                showTypingIndicator(false);
            };
            
            currentEventSource.onmessage = function(event) {
                const data = event.data;
                
                // 处理不同类型的事件
                if (data.startsWith('提问::')) {
                    handleUserInputRequest(data);
                } else if (data === '工作流执行完成') {
                    showStatus('connected', '响应完成');
                    toggleSendButton(true);
                    currentEventSource.close();
                } else if (!data.includes('[') && !data.includes('keep_alive')) {
                    // 累积AI响应
                    assistantMessage += data;
                    
                    // 创建或更新消息元素
                    if (!messageElement) {
                        messageElement = addMessage('assistant', assistantMessage);
                    } else {
                        updateMessage(messageElement, assistantMessage);
                    }
                }
            };
            
            currentEventSource.addEventListener('chat_response', function(event) {
                assistantMessage += event.data;
                if (!messageElement) {
                    messageElement = addMessage('assistant', assistantMessage);
                } else {
                    updateMessage(messageElement, assistantMessage);
                }
            });
            
            currentEventSource.addEventListener('error', function(event) {
                showStatus('error', '连接错误: ' + event.data);
                toggleSendButton(true);
                showTypingIndicator(false);
            });
            
            currentEventSource.addEventListener('complete', function(event) {
                showStatus('connected', '响应完成');
                toggleSendButton(true);
                currentEventSource.close();
            });
            
            currentEventSource.onerror = function(error) {
                console.error('EventSource error:', error);
                showStatus('error', '连接异常');
                toggleSendButton(true);
                showTypingIndicator(false);
                currentEventSource.close();
            };
        }

        // 处理用户输入请求
        function handleUserInputRequest(data) {
            const parts = data.split('::');
            if (parts.length >= 3) {
                currentInteractionId = parts[1];
                const question = parts[2];
                showQuestionModal(question);
            }
        }

        // 显示问题弹窗
        function showQuestionModal(question) {
            document.getElementById('questionTitle').textContent = question;
            document.getElementById('questionInput').value = '';
            document.getElementById('questionModal').style.display = 'block';
        }

        // 提交回答
        function submitAnswer() {
            const answer = document.getElementById('questionInput').value.trim();
            if (!answer) return;
            
            fetch('/api/chat/submit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    interactionId: currentInteractionId,
                    answer: answer
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addMessage('user', answer);
                    hideQuestionModal();
                } else {
                    alert('提交失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('提交回答失败:', error);
                alert('提交失败，请重试');
            });
        }

        // 取消问题
        function cancelQuestion() {
            hideQuestionModal();
            toggleSendButton(true);
        }

        // 隐藏问题弹窗
        function hideQuestionModal() {
            document.getElementById('questionModal').style.display = 'none';
            currentInteractionId = null;
        }

        // 添加消息
        function addMessage(type, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = type === 'user' ? 'U' : 'AI';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return contentDiv;
        }

        // 更新消息内容
        function updateMessage(element, content) {
            element.textContent = content;
            
            // 滚动到底部
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 显示状态
        function showStatus(type, message) {
            const statusIndicator = document.getElementById('statusIndicator');
            statusIndicator.className = `status-indicator ${type}`;
            statusIndicator.textContent = message;
            statusIndicator.style.display = 'block';
            
            // 3秒后隐藏状态
            setTimeout(() => {
                statusIndicator.style.display = 'none';
            }, 3000);
        }

        // 切换发送按钮状态
        function toggleSendButton(enabled) {
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = !enabled;
            sendButton.textContent = enabled ? '发送' : '发送中...';
        }

        // 显示/隐藏输入指示器
        function showTypingIndicator(show) {
            const indicator = document.getElementById('typingIndicator');
            indicator.className = show ? 'typing-indicator show' : 'typing-indicator';
        }

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            if (currentEventSource) {
                currentEventSource.close();
            }
        });
    </script>
</body>
</html>
