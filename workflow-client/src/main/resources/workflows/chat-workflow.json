{"id": "chat-workflow", "name": "智能聊天工作流", "description": "支持流式响应的智能聊天工作流", "version": "1.0.0", "type": "sequence", "children": [{"id": "input-validation", "name": "输入验证节点", "type": "com.example.node.InputValidationNode", "description": "验证用户输入的有效性"}, {"id": "chat-processor", "name": "聊天处理节点", "type": "com.example.node.ChatNode", "description": "处理聊天逻辑并生成响应", "model": "chat-gpt-3.5", "systemPrompt": "你是一个友善、专业的AI助手，能够回答各种问题并提供有用的建议。", "maxTokens": 500, "streamEnabled": true}, {"id": "response-formatter", "name": "响应格式化节点", "type": "com.example.node.ResponseFormatterNode", "description": "格式化最终响应"}]}