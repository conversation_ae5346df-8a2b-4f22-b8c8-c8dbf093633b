server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: workflow-client
  
  # WebFlux 配置
  webflux:
    base-path: /
  
  # Jackson 配置
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# 日志配置
logging:
  level:
    com.example: DEBUG
    com.stationdm.workflow: INFO
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 工作流配置
workflow:
  engine:
    # 工作流定义文件路径
    definition-path: classpath:workflows/
    # 默认超时时间（秒）
    default-timeout: 300
  
  # 交互管理配置
  interaction:
    # 交互超时时间（分钟）
    timeout-minutes: 5
    # 清理间隔（分钟）
    cleanup-interval: 10

# 聊天配置
chat:
  # 默认模型
  default-model: chat-gpt-3.5
  # 最大token数
  max-tokens: 1000
  # 流式响应延迟（毫秒）
  stream-delay: 80
  # 是否启用流式响应
  stream-enabled: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
