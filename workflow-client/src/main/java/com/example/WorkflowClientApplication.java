package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Workflow SDK 客户端应用
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication(scanBasePackages = {
    "com.example",           // 扫描客户端代码
    "com.stationdm.workflow" // 扫描SDK组件
})
public class WorkflowClientApplication {

    public static void main(String[] args) {
        SpringApplication.run(WorkflowClientApplication.class, args);
        
        System.out.println("\n" + "=".repeat(60));
        System.out.println("🚀 Workflow Client Application Started Successfully!");
        System.out.println("=".repeat(60));
        System.out.println("📋 Available Endpoints:");
        System.out.println("   • Chat Workflow: http://localhost:8080/api/chat/stream?input=你好");
        System.out.println("   • Submit Answer: POST http://localhost:8080/api/chat/submit");
        System.out.println("   • Health Check:  http://localhost:8080/api/health");
        System.out.println("   • Test Page:     http://localhost:8080/chat-demo.html");
        System.out.println("=".repeat(60) + "\n");
    }
}
