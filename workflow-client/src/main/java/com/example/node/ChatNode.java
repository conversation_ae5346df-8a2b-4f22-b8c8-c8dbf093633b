package com.example.node;

import com.fasterxml.jackson.databind.JsonNode;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.engine.annotation.Evaluate;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 智能聊天节点 - 简化版
 * 只支持流式响应
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */
@Slf4j
@Getter
@Setter
public class ChatNode extends WorkflowNode {

    /**
     * 聊天模型名称
     */
    private String model;

    @Evaluate
    private JsonNode input;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        log.info("🤖 开始执行聊天节点 - ID: {}, Model: {}", getId(), model);

        sendStreamingMessage("正在思考您的问题...", context);

        // 获取用户输入
        String userInput = null;
        if (userInput == null || userInput.trim().isEmpty()) {
            return requestUserInputAndProcess(context);
        }

        // 使用流式响应
        return generateStreamingResponse(userInput, context);
    }

    /**
     * 请求用户输入并处理
     */
    private Mono<Void> requestUserInputAndProcess(WorkflowContext context) {
        String prompt = "请告诉我您想要聊什么话题？";

        return requestUserInput(prompt, context)
                .flatMap(input -> {
                    log.info("📝 收到用户输入: {}", input);
                    return generateStreamingResponse(input, context);
                });
    }

    /**
     * 生成流式响应
     */
    private Mono<Void> generateStreamingResponse(String userInput, WorkflowContext context) {
        String fullResponse = generateResponseContent(userInput);
        List<String> tokens = tokenizeResponse(fullResponse);

        return Flux.fromIterable(tokens)
                .delayElements(Duration.ofMillis(50 + new Random().nextInt(100))) // 随机延迟50-150ms
                .doOnNext(token -> {
                    // 发送流式token
                    context.emitStreamingEvent("CHAT_RESPONSE", getId(), token);
                })
                .doOnComplete(() -> {
                    // 发送完成事件
                    context.emitStreamingEvent("CHAT_COMPLETE", getId(), "响应生成完成");
                    sendStreamingMessage("AI响应生成完成", context);

                    log.info("✅ 聊天响应生成完成 - Length: {}", fullResponse.length());
                })
                .then();
    }

    /**
     * 生成响应内容
     */
    private String generateResponseContent(String userInput) {
        // 简单的规则引擎，根据用户输入生成不同类型的响应
        String lowerInput = userInput.toLowerCase();
        
        if (lowerInput.contains("你好") || lowerInput.contains("hello")) {
            return generateGreetingResponse();
        } else if (lowerInput.contains("天气")) {
            return generateWeatherResponse();
        } else if (lowerInput.contains("技术") || lowerInput.contains("编程")) {
            return generateTechResponse();
        } else if (lowerInput.contains("帮助") || lowerInput.contains("help")) {
            return generateHelpResponse();
        } else {
            return generateGeneralResponse(userInput);
        }
    }

    /**
     * 生成问候响应
     */
    private String generateGreetingResponse() {
        String[] greetings = {
            "您好！很高兴与您交流。我是您的AI助手，有什么可以帮助您的吗？",
            "你好！欢迎使用我们的智能聊天系统。请告诉我您需要什么帮助。",
            "Hi！我是AI助手，随时为您提供帮助和建议。有什么问题尽管问我吧！"
        };
        return greetings[new Random().nextInt(greetings.length)];
    }

    /**
     * 生成天气响应
     */
    private String generateWeatherResponse() {
        return "关于天气查询，我可以为您提供帮助。不过我需要知道您想查询哪个城市的天气信息。" +
               "请告诉我具体的城市名称，我会为您提供详细的天气预报，包括温度、湿度、风力等信息。";
    }

    /**
     * 生成技术响应
     */
    private String generateTechResponse() {
        return "技术问题是我的专长之一！无论是编程语言、框架选择、架构设计还是最佳实践，" +
               "我都可以为您提供专业的建议。请具体描述您遇到的技术挑战，我会尽力帮助您找到解决方案。";
    }

    /**
     * 生成帮助响应
     */
    private String generateHelpResponse() {
        return "我很乐意为您提供帮助！我可以协助您解决各种问题，包括但不限于：\n" +
               "• 技术咨询和编程问题\n" +
               "• 学习建议和资源推荐\n" +
               "• 日常问题解答\n" +
               "• 创意思考和头脑风暴\n" +
               "请告诉我您具体需要什么帮助，我会尽我所能为您提供支持。";
    }

    /**
     * 生成通用响应
     */
    private String generateGeneralResponse(String userInput) {
        return String.format("您提到了「%s」，这确实是一个值得深入探讨的话题。" +
                           "让我来为您分析一下这个问题的几个关键方面：\n\n" +
                           "首先，我们需要理解问题的核心要点和背景信息。" +
                           "然后，我们可以从不同的角度来分析可能的解决方案。" +
                           "最后，我会为您提供一些实用的建议和下一步的行动计划。\n\n" +
                           "如果您需要更具体的信息或有其他相关问题，请随时告诉我！", 
                           userInput);
    }

    /**
     * 将响应内容分词
     */
    private List<String> tokenizeResponse(String response) {
        // 简单的分词策略：按字符分割，保留标点符号和空格
        return Arrays.asList(response.split(""));
    }

    /**
     * 获取用户输入
     */
    private String getUserInput(WorkflowContext context) {
        Object userInput = context.getNodeResult("userInput");
        return userInput != null ? userInput.toString() : null;
    }
}
