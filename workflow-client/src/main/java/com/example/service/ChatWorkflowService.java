package com.example.service;

import com.stationdm.workflow.engine.WorkflowEngineService;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.interaction.InteractionManager;
import com.stationdm.workflow.streaming.StreamingEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Set;
import java.util.UUID;

/**
 * 聊天工作流服务
 * 封装工作流引擎，提供高级聊天功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatWorkflowService {

    private final WorkflowEngineService workflowEngineService;
    private final InteractionManager interactionManager;
    private final ApplicationContext applicationContext;

    /**
     * 启动聊天工作流
     * 
     * @param userInput 用户输入
     * @return 流式响应
     */
    public Flux<ServerSentEvent<String>> startChatWorkflow(String userInput) {
        String sessionId = generateSessionId();
        
        log.info("🎯 创建聊天会话 - SessionID: {}, Input: {}", sessionId, userInput);
        
        return Flux.<ServerSentEvent<String>>create(sink -> {
            try {
                // 创建工作流上下文
                WorkflowContext context = createWorkflowContext(sessionId, userInput);
                
                // 监听工作流事件流
                subscribeToWorkflowEvents(context, sink, sessionId);
                
                // 执行聊天工作流
                executeWorkflow(context, sink, sessionId);
                
            } catch (Exception e) {
                log.error("❌ 启动聊天工作流失败 - SessionID: {}", sessionId, e);
                emitErrorEvent(sink, "启动工作流失败: " + e.getMessage());
            }
        })
        .mergeWith(createKeepAliveStream())
        .doOnCancel(() -> log.info("🔌 客户端取消订阅 - SessionID: {}", sessionId))
        .onErrorResume(error -> handleWorkflowError(error, sessionId));
    }

    /**
     * 提交用户回答
     * 
     * @param interactionId 交互ID
     * @param answer 用户回答
     * @return 提交结果
     */
    public Mono<Boolean> submitUserAnswer(String interactionId, String answer) {
        log.info("📝 处理用户回答 - InteractionID: {}, Answer: {}", interactionId, answer);
        
        return Mono.fromCallable(() -> interactionManager.submitUserInput(interactionId, answer))
                .doOnSuccess(success -> {
                    if (success) {
                        log.info("✅ 用户回答提交成功 - InteractionID: {}", interactionId);
                    } else {
                        log.warn("⚠️ 用户回答提交失败 - InteractionID: {}", interactionId);
                    }
                })
                .onErrorResume(error -> {
                    log.error("❌ 提交用户回答异常 - InteractionID: {}", interactionId, error);
                    return Mono.just(false);
                });
    }

    /**
     * 获取待处理的交互列表
     * 
     * @return 交互ID集合
     */
    public Mono<Set<String>> getPendingInteractions() {
        return Mono.fromCallable(interactionManager::getPendingInteractionIds)
                .doOnSuccess(interactions -> log.debug("📋 当前待处理交互数量: {}", interactions.size()));
    }

    /**
     * 创建工作流上下文
     */
    private WorkflowContext createWorkflowContext(String sessionId, String userInput) {
        WorkflowContext context = new WorkflowContext(applicationContext);
        context.addNodeResult("sessionId", sessionId);
        context.addNodeResult("userInput", userInput);
        context.addNodeResult("timestamp", System.currentTimeMillis());
        
        log.debug("🔧 工作流上下文创建完成 - SessionID: {}", sessionId);
        return context;
    }

    /**
     * 订阅工作流事件流
     */
    private void subscribeToWorkflowEvents(WorkflowContext context, 
                                         reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink,
                                         String sessionId) {
        
        context.getEventStream().subscribe(
            event -> handleStreamingEvent(event, sink, sessionId),
            error -> {
                log.error("❌ 工作流事件流异常 - SessionID: {}", sessionId, error);
                emitErrorEvent(sink, "事件流异常: " + error.getMessage());
            },
            () -> {
                log.info("✅ 工作流事件流完成 - SessionID: {}", sessionId);
                emitCompleteEvent(sink);
            }
        );
    }

    /**
     * 执行工作流
     */
    private void executeWorkflow(WorkflowContext context, 
                               reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink,
                               String sessionId) {
        
        // 这里使用一个示例工作流名称，实际项目中应该根据业务需求选择
        String workflowName = "chat-workflow";
        
        workflowEngineService.executeWorkflow(workflowName, context)
            .subscribe(
                result -> {
                    log.info("✅ 工作流执行成功 - SessionID: {}", sessionId);
                    context.completeEventStream();
                },
                error -> {
                    log.error("❌ 工作流执行失败 - SessionID: {}", sessionId, error);
                    emitErrorEvent(sink, "工作流执行失败: " + error.getMessage());
                    context.completeEventStream();
                }
            );
    }

    /**
     * 处理流式事件
     */
    private void handleStreamingEvent(StreamingEvent event, 
                                    reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink,
                                    String sessionId) {
        
        String eventType = event.getEventType();
        Object data = event.getData();
        String nodeId = event.getNodeId();
        
        log.debug("📡 处理流式事件 - SessionID: {}, Type: {}, NodeID: {}", sessionId, eventType, nodeId);
        
        switch (eventType) {
            case "REQUEST_USER_INPUT" -> handleUserInputRequest(event, sink, sessionId);
            case "NODE_MESSAGE" -> emitNodeMessage(sink, data.toString());
            case "CHAT_RESPONSE" -> emitChatResponse(sink, data.toString());
            case "NODE_ERROR" -> emitErrorEvent(sink, "节点错误: " + data);
            default -> emitGenericEvent(sink, eventType, data.toString());
        }
    }

    /**
     * 处理用户输入请求
     */
    private void handleUserInputRequest(StreamingEvent event, 
                                      reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink,
                                      String sessionId) {
        
        String interactionId = sessionId + "_" + event.getNodeId();
        String question = event.getData().toString();
        
        String structuredQuestion = String.format("提问::%s::%s", interactionId, question);
        
        sink.next(ServerSentEvent.<String>builder()
                .event("user_input_request")
                .data(structuredQuestion)
                .build());
        
        log.info("❓ 发送用户交互请求 - SessionID: {}, InteractionID: {}", sessionId, interactionId);
    }

    /**
     * 发送聊天响应事件
     */
    private void emitChatResponse(reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink, String response) {
        sink.next(ServerSentEvent.<String>builder()
                .event("chat_response")
                .data(response)
                .build());
    }

    /**
     * 发送节点消息事件
     */
    private void emitNodeMessage(reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink, String message) {
        sink.next(ServerSentEvent.<String>builder()
                .event("node_message")
                .data(message)
                .build());
    }

    /**
     * 发送通用事件
     */
    private void emitGenericEvent(reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink, 
                                String eventType, String data) {
        sink.next(ServerSentEvent.<String>builder()
                .event(eventType)
                .data(data)
                .build());
    }

    /**
     * 发送错误事件
     */
    private void emitErrorEvent(reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink, String errorMessage) {
        sink.next(ServerSentEvent.<String>builder()
                .event("error")
                .data(errorMessage)
                .build());
    }

    /**
     * 发送完成事件
     */
    private void emitCompleteEvent(reactor.core.publisher.FluxSink<ServerSentEvent<String>> sink) {
        sink.next(ServerSentEvent.<String>builder()
                .event("complete")
                .data("工作流执行完成")
                .build());
        sink.complete();
    }

    /**
     * 创建保活流
     */
    private Flux<ServerSentEvent<String>> createKeepAliveStream() {
        return Flux.interval(Duration.ofSeconds(30))
                .map(i -> ServerSentEvent.<String>builder()
                        .event("keep_alive")
                        .data("ping")
                        .build());
    }

    /**
     * 处理工作流异常
     */
    private Flux<ServerSentEvent<String>> handleWorkflowError(Throwable error, String sessionId) {
        log.error("🔥 工作流异常 - SessionID: {}", sessionId, error);
        
        return Flux.just(
                ServerSentEvent.<String>builder()
                        .event("error")
                        .data("工作流异常: " + error.getMessage())
                        .build(),
                ServerSentEvent.<String>builder()
                        .event("close")
                        .data("连接已关闭")
                        .build()
        );
    }

    /**
     * 生成会话ID
     */
    private String generateSessionId() {
        return "chat_" + UUID.randomUUID().toString().replace("-", "").substring(0, 12);
    }
}
