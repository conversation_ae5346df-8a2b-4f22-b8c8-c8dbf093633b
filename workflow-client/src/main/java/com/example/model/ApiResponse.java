package com.example.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 统一API响应模型
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApiResponse<T> {

    /**
     * 响应状态码
     */
    private int code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应时间戳
     */
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();

    /**
     * 创建成功响应
     * 
     * @param message 响应消息
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.<T>builder()
                .code(200)
                .message(message)
                .data(data)
                .success(true)
                .build();
    }

    /**
     * 创建成功响应（无数据）
     * 
     * @param message 响应消息
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message) {
        return success(message, null);
    }

    /**
     * 创建错误响应
     * 
     * @param message 错误消息
     * @param data 错误数据
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String message, T data) {
        return ApiResponse.<T>builder()
                .code(500)
                .message(message)
                .data(data)
                .success(false)
                .build();
    }

    /**
     * 创建错误响应（无数据）
     * 
     * @param message 错误消息
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return error(message, null);
    }

    /**
     * 创建自定义响应
     * 
     * @param code 状态码
     * @param message 响应消息
     * @param data 响应数据
     * @param success 是否成功
     * @return 自定义响应
     */
    public static <T> ApiResponse<T> custom(int code, String message, T data, boolean success) {
        return ApiResponse.<T>builder()
                .code(code)
                .message(message)
                .data(data)
                .success(success)
                .build();
    }
}
