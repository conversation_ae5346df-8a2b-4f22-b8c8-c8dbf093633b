package com.example.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;

/**
 * 聊天提交请求模型
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatSubmitRequest {

    /**
     * 交互ID
     */
    @NotBlank(message = "交互ID不能为空")
    private String interactionId;

    /**
     * 用户回答
     */
    @NotBlank(message = "回答内容不能为空")
    private String answer;
}
