package com.example.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 聊天请求模型
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatRequest {

    /**
     * 用户输入内容
     */
    @NotBlank(message = "输入内容不能为空")
    @Size(max = 1000, message = "输入内容不能超过1000个字符")
    private String input;

    /**
     * 会话上下文（可选）
     */
    private String context;

    /**
     * 用户ID（可选）
     */
    private String userId;
}
