package com.example.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stationdm.workflow.engine.WorkflowEngineService;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.handler.ChatHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * 聊天工作流控制器
 * 基于 WorkflowEngineService 实现流式聊天
 */
@Slf4j
@RestController
@RequestMapping("/api/chat")
public class ChatWorkflowController {

    @Autowired
    private WorkflowEngineService workflowEngineService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ChatHandler chatHandler;

    /**
     * 启动聊天工作流 - 支持流式响应
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> startChatStream(@RequestParam String input) {
        String sessionId = UUID.randomUUID().toString();
        log.info("启动聊天工作流 - SessionID: {}, Input: {}", sessionId, input);

        return Flux.<ServerSentEvent<String>>create(sink -> {
                    try {
                        // 创建工作流上下文
                        WorkflowContext context = new WorkflowContext(applicationContext);
                        context.addNodeResult("userInput", input);
                        context.addNodeResult("sessionId", sessionId);

                        // 监听工作流事件流
                        context.getEventStream().subscribe(
                                event -> {
                                    // 处理交互请求事件
                                    if ("REQUEST_USER_INPUT".equals(event.getEventType())) {
                                        String interactionId = sessionId + "_" + event.getNodeId();
                                        String question = event.getData().toString();

                                        // 发送结构化提问格式
                                        String structuredQuestion = String.format("提问::%s::%s", interactionId, question);
                                        sink.next(ServerSentEvent.<String>builder().data(structuredQuestion).build());

                                        log.info("发送交互请求 - InteractionID: {}, Question: {}", interactionId, question);
                                    } else if ("CHAT_RESPONSE".equals(event.getEventType())) {
                                        // 处理聊天响应 - 逐字符流式返回
                                        String response = event.getData().toString();
                                        sink.next(ServerSentEvent.<String>builder().data(response).build());
                                    } else {
                                        // 其他事件直接转发
                                        sink.next(ServerSentEvent.<String>builder().data(event.getEventType() + ": " + event.getData()).build());
                                    }
                                },
                                error -> {
                                    log.error("事件流错误", error);
                                    sink.next(ServerSentEvent.<String>builder().data("错误: " + error.getMessage()).build());
                                    sink.error(error);
                                },
                                () -> {
                                    log.info("聊天工作流完成");
                                    sink.next(ServerSentEvent.<String>builder().data("聊天完成").build());
                                    sink.complete();
                                }
                        );

                        // 使用 WorkflowEngineService 执行聊天工作流
                        try {
                            workflowEngineService.execute("chat-workflow", input, context)
                                    .subscribe(
                                            v -> {
                                                log.info("聊天工作流执行成功");
                                                context.completeEventStream();
                                            },
                                            error -> {
                                                log.error("聊天工作流执行失败", error);
                                                sink.next(ServerSentEvent.<String>builder().data("执行失败: " + error.getMessage()).build());
                                                context.completeEventStream();
                                            }
                                    );
                        } catch (JsonProcessingException e) {
                            log.error("工作流JSON解析失败 - SessionID: {}", sessionId, e);
                            sink.next(ServerSentEvent.<String>builder().data("工作流配置错误: " + e.getMessage()).build());
                            sink.error(e);
                        }

                    } catch (Exception e) {
                        log.error("启动聊天工作流失败 - SessionID: {}", sessionId, e);
                        sink.next(ServerSentEvent.<String>builder().data("启动失败: " + e.getMessage()).build());
                        sink.error(e);
                    }
                })
                .mergeWith(Flux.interval(java.time.Duration.ofSeconds(30))
                        .map(i -> ServerSentEvent.<String>builder().data("keep-alive").build()));
    }

    /**
     * 提交用户输入
     */
    @PostMapping("/submit")
    public Mono<String> submitUserInput(
            @RequestParam String interactionId,
            @RequestParam String answer) {

        log.info("收到用户输入 - InteractionID: {}, Answer: {}", interactionId, answer);

        boolean success = chatHandler.submitUserInput(interactionId, answer);
        return Mono.just(success ? "提交成功" : "提交失败");
    }
}
