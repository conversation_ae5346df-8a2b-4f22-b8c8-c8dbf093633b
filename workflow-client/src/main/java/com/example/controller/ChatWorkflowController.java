package com.example.controller;

import com.example.service.ChatWorkflowService;
import com.example.model.ChatRequest;
import com.example.model.ChatSubmitRequest;
import com.example.model.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * 聊天工作流控制器
 * 提供优雅的聊天交互API
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/chat")
@RequiredArgsConstructor
@Validated
public class ChatWorkflowController {

    private final ChatWorkflowService chatWorkflowService;

    /**
     * 启动聊天工作流 - 流式响应
     * 
     * @param input 用户输入
     * @return 流式聊天响应
     */
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> startChatStream(
            @RequestParam @NotBlank(message = "输入内容不能为空") String input) {
        
        log.info("🚀 启动聊天工作流 - Input: {}", input);
        
        return chatWorkflowService.startChatWorkflow(input)
                .doOnSubscribe(subscription -> log.debug("📡 客户端开始订阅流式响应"))
                .doOnComplete(() -> log.info("✅ 聊天工作流完成"))
                .doOnError(error -> log.error("❌ 聊天工作流异常", error))
                .onErrorResume(this::handleStreamError);
    }

    /**
     * 启动聊天工作流 - POST方式
     * 
     * @param request 聊天请求
     * @return 流式聊天响应
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> startChatStreamPost(@Valid @RequestBody ChatRequest request) {
        
        log.info("🚀 启动聊天工作流 - Request: {}", request);
        
        return chatWorkflowService.startChatWorkflow(request.getInput())
                .doOnSubscribe(subscription -> log.debug("📡 客户端开始订阅流式响应"))
                .doOnComplete(() -> log.info("✅ 聊天工作流完成"))
                .doOnError(error -> log.error("❌ 聊天工作流异常", error))
                .onErrorResume(this::handleStreamError);
    }

    /**
     * 提交用户回答
     * 
     * @param request 提交请求
     * @return 提交结果
     */
    @PostMapping("/submit")
    public Mono<ApiResponse<String>> submitAnswer(@Valid @RequestBody ChatSubmitRequest request) {
        
        log.info("📝 收到用户回答 - InteractionID: {}, Answer: {}", 
                request.getInteractionId(), request.getAnswer());
        
        return chatWorkflowService.submitUserAnswer(request.getInteractionId(), request.getAnswer())
                .map(success -> success 
                    ? ApiResponse.success("回答提交成功", "已成功提交您的回答")
                    : ApiResponse.error("提交失败", "未找到对应的交互会话"))
                .doOnSuccess(response -> log.info("✅ 回答提交结果: {}", response.isSuccess()))
                .onErrorResume(error -> {
                    log.error("❌ 提交回答异常", error);
                    return Mono.just(ApiResponse.error("系统异常", error.getMessage()));
                });
    }

    /**
     * 获取待处理的交互列表
     * 
     * @return 待处理交互ID列表
     */
    @GetMapping("/pending")
    public Mono<ApiResponse<Object>> getPendingInteractions() {
        
        log.debug("📋 查询待处理交互列表");
        
        return chatWorkflowService.getPendingInteractions()
                .map(interactions -> ApiResponse.success("查询成功", interactions))
                .onErrorResume(error -> {
                    log.error("❌ 查询待处理交互异常", error);
                    return Mono.just(ApiResponse.error("查询失败", error.getMessage()));
                });
    }

    /**
     * 健康检查
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Mono<ApiResponse<String>> healthCheck() {
        return Mono.just(ApiResponse.success("服务正常", "Chat Workflow Service is running"));
    }

    /**
     * 处理流式响应异常
     * 
     * @param error 异常信息
     * @return 错误响应流
     */
    private Flux<ServerSentEvent<String>> handleStreamError(Throwable error) {
        log.error("🔥 流式响应异常", error);
        
        return Flux.just(
                ServerSentEvent.<String>builder()
                        .event("error")
                        .data("系统异常: " + error.getMessage())
                        .build(),
                ServerSentEvent.<String>builder()
                        .event("close")
                        .data("连接已关闭")
                        .build()
        );
    }
}
