# Workflow Client Application

一个基于 Workflow SDK 的优雅聊天客户端应用，展示如何集成和使用工作流引擎。

## 🚀 快速开始

### 1. 构建 SDK
```bash
# 在 SDK 项目根目录执行
cd ../
mvn clean install
```

### 2. 启动客户端应用
```bash
# 进入客户端目录
cd workflow-client

# 启动应用
mvn spring-boot:run
```

### 3. 访问应用
- **聊天演示页面**: http://localhost:8080/chat-demo.html
- **API 文档**: http://localhost:8080/api/chat/health

## 📋 API 接口

### 聊天相关接口

#### 1. 启动聊天流 (GET)
```bash
curl -N "http://localhost:8080/api/chat/stream?input=你好"
```

#### 2. 启动聊天流 (POST)
```bash
curl -X POST "http://localhost:8080/api/chat/stream" \
  -H "Content-Type: application/json" \
  -d '{"input": "你好", "userId": "user123"}'
```

#### 3. 提交用户回答
```bash
curl -X POST "http://localhost:8080/api/chat/submit" \
  -H "Content-Type: application/json" \
  -d '{"interactionId": "chat_xxx_yyy", "answer": "北京"}'
```

#### 4. 查看待处理交互
```bash
curl "http://localhost:8080/api/chat/pending"
```

#### 5. 健康检查
```bash
curl "http://localhost:8080/api/chat/health"
```

## 🏗️ 项目结构

```
workflow-client/
├── src/main/java/com/example/
│   ├── WorkflowClientApplication.java    # 启动类
│   ├── controller/
│   │   └── ChatWorkflowController.java   # 聊天控制器
│   ├── service/
│   │   └── ChatWorkflowService.java      # 聊天服务
│   ├── model/
│   │   ├── ChatRequest.java              # 聊天请求模型
│   │   ├── ChatSubmitRequest.java        # 提交请求模型
│   │   └── ApiResponse.java              # 统一响应模型
│   └── node/
│       └── ChatNode.java                 # 聊天节点实现
├── src/main/resources/
│   ├── application.yml                   # 应用配置
│   ├── workflows/
│   │   └── chat-workflow.json           # 工作流定义
│   └── static/
│       └── chat-demo.html               # 演示页面
└── pom.xml                              # Maven 配置
```

## 🎯 核心特性

### 1. 优雅的 API 设计
- 统一的响应格式
- 完善的参数验证
- 详细的错误处理
- RESTful 风格接口

### 2. 流式聊天体验
- Server-Sent Events 实时推送
- 逐字符流式响应
- 打字机效果展示
- 用户交互支持

### 3. 智能聊天节点
- 基于规则的响应生成
- 支持多种话题识别
- 可配置的流式参数
- 模拟真实 AI 体验

### 4. 完整的前端演示
- 现代化 UI 设计
- 实时消息展示
- 交互式问答支持
- 响应式布局

## 🔧 配置说明

### application.yml 主要配置

```yaml
# 服务器配置
server:
  port: 8080

# 工作流配置
workflow:
  engine:
    definition-path: classpath:workflows/
    default-timeout: 300
  interaction:
    timeout-minutes: 5

# 聊天配置
chat:
  default-model: chat-gpt-3.5
  max-tokens: 1000
  stream-delay: 80
  stream-enabled: true
```

## 🎨 自定义开发

### 1. 创建自定义节点
```java
@Getter
@Setter
public class CustomChatNode extends WorkflowNode {
    @Override
    public Mono<Void> execute(WorkflowContext context) {
        // 实现自定义逻辑
        return Mono.empty();
    }
}
```

### 2. 扩展聊天服务
```java
@Service
public class EnhancedChatService extends ChatWorkflowService {
    // 扩展功能
}
```

### 3. 添加新的工作流
在 `src/main/resources/workflows/` 目录下添加新的 JSON 工作流定义文件。

## 🧪 测试

### 1. 单元测试
```bash
mvn test
```

### 2. 集成测试
```bash
mvn verify
```

### 3. 手动测试
使用提供的 HTML 演示页面进行交互式测试。

## 📝 开发指南

### 1. 添加新的 API 接口
- 在 `ChatWorkflowController` 中添加新的端点
- 在 `ChatWorkflowService` 中实现业务逻辑
- 添加相应的请求/响应模型

### 2. 扩展聊天功能
- 修改 `ChatNode` 的响应生成逻辑
- 添加新的事件类型处理
- 扩展前端页面功能

### 3. 集成外部 AI 服务
- 在 `ChatNode` 中添加 HTTP 客户端调用
- 处理外部 API 的流式响应
- 实现错误重试和降级机制

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License
