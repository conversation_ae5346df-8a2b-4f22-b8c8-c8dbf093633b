package com.stationdm.workflow.example;

import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 天气查询节点示例
 * 演示普通节点如何具备交互能力
 * 当参数不完整时，会主动请求用户输入
 */
@Slf4j
@Getter
@Setter
public class WeatherQueryNode extends WorkflowNode {
    
    private String city;
    private String date;

    @Override
    public Mono<Void> execute(WorkflowContext context) {
        sendStreamingMessage("开始执行天气查询节点", context);
        return validateAndQuery(context);
    }
    
    /**
     * 验证参数并查询天气
     * 如果参数不完整，会请求用户输入
     */
    private Mono<Void> validateAndQuery(WorkflowContext context) {
        // 检查城市参数
        if (city == null || city.trim().isEmpty()) {
            sendStreamingMessage("城市参数缺失，请求用户输入...", context);
            return requestUserInput("请提供需要查询天气的城市名称：", context)
                .doOnNext(userCity -> {
                    this.city = userCity.trim();
                    sendStreamingMessage("用户输入城市: " + this.city, context);
                })
                .then(Mono.defer(() -> validateAndQuery(context)));
        }
        
        // 检查日期参数
        if (date == null || date.trim().isEmpty()) {
            sendStreamingMessage("日期参数缺失，请求用户输入...", context);
            return requestUserInput("请提供需要查询天气的日期 (例如 2023-10-01):", context)
                .doOnNext(userDate -> {
                    this.date = userDate.trim();
                    sendStreamingMessage("用户输入日期: " + this.date, context);
                })
                .then(Mono.defer(() -> validateAndQuery(context)));
        }
        
        // 参数完整，执行查询
        return executeWeatherQuery(context);
    }
    
    /**
     * 执行天气查询
     */
    private Mono<Void> executeWeatherQuery(WorkflowContext context) {
        sendStreamingMessage("正在查询天气信息...", context);
        
        return Mono.fromCallable(() -> {
            // 模拟天气查询
            String weatherInfo = String.format("在 %s, %s 的天气是：晴朗，温度 25℃。", city, date);
            
            // 保存结果到上下文
            context.addNodeResult(getId() + "_result", weatherInfo);
            context.addNodeResult("weatherInfo", weatherInfo);
            
            sendStreamingMessage("天气查询完成: " + weatherInfo, context);
            
            // 发送结构化结果事件
            context.emitStreamingEvent("WEATHER_QUERY_COMPLETED", getId(), 
                java.util.Map.of(
                    "city", city,
                    "date", date,
                    "weather", weatherInfo
                ));
            
            return null;
        })
        .onErrorResume(error -> {
            sendStreamingMessage("天气查询失败: " + error.getMessage(), context);
            context.emitStreamingEvent("NODE_ERROR", getId(), "天气查询失败: " + error.getMessage());
            return Mono.error(error);
        })
        .then();
    }
}
