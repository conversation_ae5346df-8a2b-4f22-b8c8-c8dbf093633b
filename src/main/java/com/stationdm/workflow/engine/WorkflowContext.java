
package com.stationdm.workflow.engine;

import com.stationdm.workflow.streaming.StreamingEvent;
import lombok.Data;
import org.springframework.context.ApplicationContext;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;


@Data
public class WorkflowContext {

    private final Map<String, Object> data = new ConcurrentHashMap<>();
    private final ApplicationContext applicationContext;
    private final Sinks.Many<StreamingEvent> eventSink;
    private final String contextId;

    public WorkflowContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        this.eventSink = Sinks.many().multicast().onBackpressureBuffer();
        this.contextId = UUID.randomUUID().toString();
    }
    public void addNodeResult(String nodeId, Object result) {
        this.data.put(nodeId, result);
    }

    public Object getNodeResult(String nodeId) {
        return this.data.get(nodeId);
    }

    public <T> T getRequiredBean(Class<T> requiredType) {
        return this.applicationContext.getBean(requiredType);
    }

    public void emitStreamingEvent(String eventType, String nodeId, Object data) {
        StreamingEvent event = StreamingEvent.builder()
                .eventType(eventType)
                .nodeId(nodeId)
                .data(data)
                .build();
        eventSink.tryEmitNext(event);
    }
    public Flux<StreamingEvent> getEventStream() {
        return eventSink.asFlux();
    }
    public void completeEventStream() {
        eventSink.tryEmitComplete();
    }
}