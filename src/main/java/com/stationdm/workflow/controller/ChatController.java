package com.stationdm.workflow.controller;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;

@Slf4j
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    @PostMapping(produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamChat(@RequestBody JSONObject request) {
        // 调用大模型 API 并返回 Flux 流
        return callLargeModelApi(request)
                .doOnNext(chunk -> log.info("发送响应片段: {}", chunk))
                .doOnError(error -> log.error("流式处理出错", error));
    }

    // 模拟调用大模型 API，返回 Flux 流
    private Flux<String> callLargeModelApi(JSONObject prompt) {
        // 实际项目中需替换为真实的大模型调用逻辑
        return Flux.just(
                        "您好！",
                        "我是您的AI助手。",
                        "您的问题是：" + prompt,
                        "我将为您提供详细解答..."
                )
                .delayElements(Duration.ofMillis(300)); // 模拟实时响应延迟
    }
}