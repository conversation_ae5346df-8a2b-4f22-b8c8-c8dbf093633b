package com.stationdm.workflow.controller;

import com.stationdm.workflow.service.AgenticFlowService;
import com.stationdm.workflow.service.ChatNodeHandler;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/flow")
public class FlowController {

    private final AgenticFlowService flowService;
    private final ChatNodeHandler chatNodeHandler;

    public FlowController(AgenticFlowService flowService, ChatNodeHandler chatNodeHandler) {
        this.flowService = flowService;
        this.chatNodeHandler = chatNodeHandler;
    }

    @GetMapping(value = "/run", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> runFlow(@RequestParam String input) {
        return flowService.runFlow(input);
    }

    @PostMapping("/chat/submit")
    public Mono<Void> submitChat(@RequestParam String traceId, @RequestParam String answer) {
        chatNodeHandler.submitUserInput(traceId, answer);
        return Mono.empty();
    }
} 