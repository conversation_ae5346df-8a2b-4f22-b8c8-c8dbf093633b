
package com.stationdm.workflow.flow.node;

import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.ChoiceNode;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Mono;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class ConditionNode extends ChoiceNode {

    @Override
    protected Mono<Integer> calculate(WorkflowContext context) {
        return null;
    }
}