package com.stationdm.workflow.flow.base;

import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.handler.ChatHandler;
import lombok.Data;
import reactor.core.publisher.Mono;

@Data
public abstract class WorkflowNode implements WorkflowTask {
    private String id;
    private String name;
    private String type;
    protected Mono<String> requestUserInput(String prompt, WorkflowContext context) {
        String interactionId = context.getContextId() + "_" + getId();
        context.emitStreamingEvent("REQUEST_USER_INPUT", getId(), prompt);

        ChatHandler chatHandler = context.getRequiredBean(ChatHandler.class);
        return chatHandler.waitForUserInput(interactionId, prompt, 5);
    }
    protected void sendStreamingMessage(String message, WorkflowContext context) {
        context.emitStreamingEvent("NODE_MESSAGE", getId(), message);
    }
}