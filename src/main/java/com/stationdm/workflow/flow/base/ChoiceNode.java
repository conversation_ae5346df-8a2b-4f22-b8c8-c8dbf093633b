package com.stationdm.workflow.flow.base;

import com.stationdm.workflow.engine.NodeInputProcessor;
import com.stationdm.workflow.engine.WorkflowContext;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Mono;

import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public abstract class ChoiceNode extends WorkflowNode {

    private List<WorkflowNode> children;

    @Override
    public final Mono<Void> execute(WorkflowContext context) {
        // 从上下文中获取处理器，以便为子节点进行自动求值
        final var processor = context.getRequiredBean(NodeInputProcessor.class);

        return Mono.defer(() -> calculate(context))
                .map(branchIndex -> {
                    if (children == null || children.isEmpty()) {
                        return -1;
                    }
                    int max = children.size() - 1;
                    if (branchIndex < 0) return 0;
                    if (branchIndex > max) return max;
                    return branchIndex;
                })
                .flatMap(index -> {
                    if (index < 0) {
                        return Mono.empty();
                    }
                    WorkflowNode chosenChild = children.get(index);
                    // 在执行子节点前，先处理其输入，然后执行
                    return Mono.fromRunnable(() -> processor.process(chosenChild, context))
                            .then(chosenChild.execute(context));
                })
                .then();
    }

    protected abstract Mono<Integer> calculate(WorkflowContext context);
} 