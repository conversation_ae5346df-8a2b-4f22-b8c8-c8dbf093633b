package com.stationdm.workflow.service;

import com.stationdm.workflow.model.FlowContext;
import com.stationdm.workflow.service.tasks.FlowNode;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;

@Service
public class AgenticFlowService {

    private final List<FlowNode> flowNodes;

    public AgenticFlowService(List<FlowNode> flowNodes) {
        this.flowNodes = flowNodes;
    }

    public Flux<ServerSentEvent<String>> runFlow(String userInput) {
        String traceId = UUID.randomUUID().toString();
        
        return Flux.<ServerSentEvent<String>>create(sink -> {
            FlowContext context = new FlowContext();
            context.setTraceId(traceId);
            context.setUserInput(userInput);
            context.setEventSink(sink);  // 将sink存入context，以便QAChatNode等节点使用
            
            // 使用循环动态构建响应式流程链
            Mono<FlowContext> flow = Mono.just(context);
            for (FlowNode node : flowNodes) {
                flow = flow.flatMap(node::process);
            }
            
            // 订阅并处理结果
            flow.subscribe(
                finalContext -> {
                    // 推送最终结果
                    sink.next(sse("最终结果：" + finalContext.getResult()));
                    sink.complete();
                },
                error -> {
                    // 错误处理
                    sink.next(sse("错误：" + error.getMessage()));
                    sink.error(error);
                }
            );
        })
        .mergeWith(Flux.interval(java.time.Duration.ofSeconds(15)).map(i -> sse("keep-alive")))
        .onErrorResume(e -> Flux.just(sse("会话结束: " + e.getMessage())));
    }
    
    private ServerSentEvent<String> sse(String data) {
        return ServerSentEvent.builder(data).build();
    }

}