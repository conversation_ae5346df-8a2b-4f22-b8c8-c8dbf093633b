package com.stationdm.workflow.service.nodes;


import com.stationdm.workflow.service.data.LocalHeaderData;
import com.stationdm.workflow.service.data.LocalParamData;
import lombok.Data;

import java.util.List;


@Data
public class HttpNode extends BaseNode{
    private List<LocalParamData> inputParams;
    private List<LocalParamData> outputParams;
    private String method;
    private String url;
    private List<LocalHeaderData> headers;
    private List<LocalParamData> requestParams;
    private String requestBody;
    private String contentType;
    private Boolean authEnable;
    private String authType;
    private String token;
}

