package com.stationdm.workflow.service.tasks;

import com.stationdm.workflow.model.FlowContext;
import com.stationdm.workflow.model.IntentType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@Order(2)
public class IntentNode implements FlowNode {
    @Override
    public Mono<FlowContext> process(FlowContext context) {
        if (context.getUserInput().toLowerCase().contains("天气")) {
            context.setIntent(IntentType.WEATHER.name());
            log.info("2. [IntentNode] 识别意图为: {}", IntentType.WEATHER.name());
        } else {
            context.setIntent(IntentType.UNKNOWN.name());
            log.info("2. [IntentNode] 无法识别意图，标记为: {}", IntentType.UNKNOWN.name());
        }
        return Mono.just(context);
    }
} 