package com.stationdm.workflow.service.tasks;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stationdm.workflow.model.FlowContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * LLM流式响应节点
 * 集成真实的大语言模型，实现逐词流式返回
 */
@Slf4j
@Component
@Order(6)
public class LLMStreamingNode implements FlowNode {

    @Autowired
    private WebClient.Builder webClientBuilder;
    
    @Autowired
    private ObjectMapper objectMapper;

    // 配置项，可以在application.yml中设置
    @Value("${llm.api.url:https://api.openai.com/v1/chat/completions}")
    private String apiUrl;
    
    @Value("${llm.api.key:}")
    private String apiKey;
    
    @Value("${llm.model:gpt-3.5-turbo}")
    private String model;

    @Override
    public Mono<FlowContext> process(FlowContext context) {
        log.info("6. [LLMStreamingNode] 开始LLM流式响应处理");
        
        if (apiKey.isEmpty()) {
            // 如果没有配置API Key，使用模拟响应
            return processWithMockResponse(context);
        }
        
        // 使用真实的LLM API
        return processWithRealLLM(context);
    }

    /**
     * 使用真实的LLM API进行流式处理
     */
    private Mono<FlowContext> processWithRealLLM(FlowContext context) {
        WebClient webClient = webClientBuilder
                .defaultHeader("Authorization", "Bearer " + apiKey)
                .defaultHeader("Content-Type", "application/json")
                .build();

        Map<String, Object> requestBody = Map.of(
                "model", model,
                "messages", new Object[]{
                        Map.of("role", "user", "content", context.getUserInput())
                },
                "stream", true,
                "max_tokens", 1000
        );

        return webClient.post()
                .uri(apiUrl)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToFlux(String.class)
                .filter(line -> line.startsWith("data: ") && !line.contains("[DONE]"))
                .map(line -> line.substring(6)) // 移除 "data: " 前缀
                .flatMap(this::parseStreamChunk)
                .doOnNext(content -> {
                    if (context.getEventSink() != null && !content.isEmpty()) {
                        ServerSentEvent<String> event = ServerSentEvent.<String>builder()
                                .data(content)
                                .build();
                        context.getEventSink().next(event);
                    }
                })
                .reduce("", (acc, chunk) -> acc + chunk)
                .doOnNext(fullResponse -> {
                    context.setResult(fullResponse);
                    if (context.getEventSink() != null) {
                        ServerSentEvent<String> completeEvent = ServerSentEvent.<String>builder()
                                .data("[STREAM_COMPLETE]")
                                .build();
                        context.getEventSink().next(completeEvent);
                    }
                })
                .thenReturn(context)
                .onErrorResume(error -> {
                    log.error("LLM API调用失败，使用模拟响应", error);
                    return processWithMockResponse(context);
                });
    }

    /**
     * 解析流式响应块
     */
    private Flux<String> parseStreamChunk(String jsonLine) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonLine);
            JsonNode choices = jsonNode.get("choices");
            if (choices != null && choices.isArray() && choices.size() > 0) {
                JsonNode delta = choices.get(0).get("delta");
                if (delta != null && delta.has("content")) {
                    String content = delta.get("content").asText();
                    return Flux.just(content);
                }
            }
        } catch (Exception e) {
            log.debug("解析流式响应块失败: {}", e.getMessage());
        }
        return Flux.empty();
    }

    /**
     * 使用模拟响应进行流式处理
     */
    private Mono<FlowContext> processWithMockResponse(FlowContext context) {
        log.info("使用模拟LLM响应");
        
        String mockResponse = generateMockResponse(context.getUserInput());
        String[] characters = mockResponse.split("");

        return Flux.fromArray(characters)
                .delayElements(Duration.ofMillis(30)) // 每30ms发送一个字符
                .doOnNext(character -> {
                    if (context.getEventSink() != null) {
                        ServerSentEvent<String> event = ServerSentEvent.<String>builder()
                                .data(character)
                                .build();
                        context.getEventSink().next(event);
                    }
                })
                .reduce("", (acc, character) -> acc + character)
                .doOnNext(fullResponse -> {
                    context.setResult(fullResponse);
                    if (context.getEventSink() != null) {
                        ServerSentEvent<String> completeEvent = ServerSentEvent.<String>builder()
                                .data("[STREAM_COMPLETE]")
                                .build();
                        context.getEventSink().next(completeEvent);
                    }
                })
                .thenReturn(context);
    }

    /**
     * 生成模拟响应
     */
    private String generateMockResponse(String userInput) {
        return String.format(
                "您好！关于您的问题「%s」，我来为您详细解答。\n\n" +
                "首先，让我分析一下这个问题的关键要点：\n" +
                "1. 问题的核心在于理解需求\n" +
                "2. 需要考虑多个维度的因素\n" +
                "3. 提供切实可行的解决方案\n\n" +
                "基于以上分析，我的建议是：\n" +
                "- 采用渐进式的方法来处理\n" +
                "- 注意关键细节的把控\n" +
                "- 及时调整和优化策略\n\n" +
                "希望这个回答对您有所帮助！如果您还有其他问题，请随时告诉我。",
                userInput
        );
    }
}
