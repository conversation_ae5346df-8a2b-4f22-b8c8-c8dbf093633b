package com.stationdm.workflow.service.tasks;

import com.stationdm.workflow.model.FlowContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

/**
 * AI流式响应节点
 * 模拟大模型逐词返回的效果
 */
@Slf4j
@Component
@Order(5)
public class AIStreamingNode implements FlowNode {

    @Override
    public Mono<FlowContext> process(FlowContext context) {
        log.info("5. [AIStreamingNode] 开始AI流式响应处理");
        
        // 模拟AI响应内容
        String aiResponse = generateAIResponse(context.getUserInput());
        
        // 将响应按词汇分割
        List<String> words = splitIntoWords(aiResponse);
        
        // 逐词流式发送
        return streamWords(words, context)
                .then(Mono.fromRunnable(() -> {
                    context.setResult(aiResponse);
                    log.info("AI流式响应完成，总词数: {}", words.size());
                }))
                .thenReturn(context);
    }

    /**
     * 生成AI响应内容
     */
    private String generateAIResponse(String userInput) {
        // 这里可以集成真实的AI模型
        return String.format(
            "根据您的问题「%s」，我来为您详细解答。首先需要分析问题的核心要点，然后提供相应的解决方案。" +
            "这个问题涉及多个方面，需要综合考虑各种因素。让我逐步为您说明具体的处理步骤和注意事项。" +
            "希望这个回答能够帮助到您，如果还有其他疑问，请随时告诉我。", 
            userInput
        );
    }

    /**
     * 将文本分割成词汇
     */
    private List<String> splitIntoWords(String text) {
        // 按字符分割，模拟逐字返回
        return Arrays.asList(text.split(""));
    }

    /**
     * 逐词流式发送
     */
    private Mono<Void> streamWords(List<String> words, FlowContext context) {
        if (context.getEventSink() == null) {
            return Mono.empty();
        }

        return Flux.fromIterable(words)
                .delayElements(Duration.ofMillis(50)) // 每50ms发送一个字符
                .doOnNext(word -> {
                    // 发送单个词汇
                    ServerSentEvent<String> event = ServerSentEvent.<String>builder()
                            .data(word)
                            .build();
                    context.getEventSink().next(event);
                })
                .doOnComplete(() -> {
                    // 发送完成标记
                    ServerSentEvent<String> completeEvent = ServerSentEvent.<String>builder()
                            .data("[STREAM_COMPLETE]")
                            .build();
                    context.getEventSink().next(completeEvent);
                })
                .then();
    }
}
