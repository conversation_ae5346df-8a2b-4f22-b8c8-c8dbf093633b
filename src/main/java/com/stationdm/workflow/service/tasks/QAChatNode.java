package com.stationdm.workflow.service.tasks;

import com.stationdm.workflow.model.FlowContext;
import com.stationdm.workflow.model.IntentType;
import com.stationdm.workflow.service.ChatNodeHandler;
import org.springframework.core.annotation.Order;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@Order(3)
public class QAChatNode implements FlowNode {

    private final ChatNodeHandler chatNodeHandler;

    // 使用构造函数注入
    public QAChatNode(ChatNodeHandler chatNodeHandler) {
        this.chatNodeHandler = chatNodeHandler;
    }

    @Override
    public Mono<FlowContext> process(FlowContext context) {
        // 仅当意图是 WEATHER 且城市信息缺失时才触发问答
        if (IntentType.WEATHER.name().equals(context.getIntent()) && (context.getCity() == null || context.getCity().isEmpty())) {
            String question = "请提供需要查询天气的城市名称：";
            // 修改：在提问事件中包含 traceId，用特殊格式分隔
            String structuredQuestion = String.format("提问::%s::%s", context.getTraceId()+"_CHAT", question);
            
            // 使用ServerSentEvent发送问题
            if (context.getEventSink() != null) {
                context.getEventSink().next(ServerSentEvent.builder(structuredQuestion).build());
            }
            
            // 这里会暂停流程，直到用户通过 submitUserInput 提供输入
            return chatNodeHandler.waitForUserInput(context.getTraceId()+"_CHAT", question)
                    .map(cityFromUser -> {
                        context.setCity(cityFromUser);
                        return context;
                    });
        }
        // 如果不需要提问，则直接进入下一个节点
        return Mono.just(context);
    }
} 