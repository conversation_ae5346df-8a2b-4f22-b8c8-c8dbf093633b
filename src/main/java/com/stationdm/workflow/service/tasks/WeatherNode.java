package com.stationdm.workflow.service.tasks;

import com.stationdm.workflow.model.FlowContext;
import com.stationdm.workflow.model.IntentType;
import com.stationdm.workflow.service.ChatNodeHandler;
import org.springframework.core.annotation.Order;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@Order(4)
public class WeatherNode implements FlowNode {

    private final ChatNodeHandler chatNodeHandler;

    // 使用构造函数注入
    public WeatherNode(ChatNodeHandler chatNodeHandler) {
        this.chatNodeHandler = chatNodeHandler;
    }

    @Override
    public Mono<FlowContext> process(FlowContext context) {
        if (IntentType.WEATHER.name().equals(context.getIntent())) {
            // 新增：检查日期是否为空
            if (context.getDate() == null || context.getDate().isEmpty()) {
                String question = "请提供需要查询天气的日期 (例如 2023-10-01):";
                // 遵循结构化提问格式
                String structuredQuestion = String.format("提问::%s::%s", context.getTraceId() + "_WEATHER", question);

                // 使用ServerSentEvent发送问题
                if (context.getEventSink() != null) {
                    context.getEventSink().next(ServerSentEvent.builder(structuredQuestion).build());
                }

                return chatNodeHandler.waitForUserInput(context.getTraceId() + "_WEATHER", question)
                        .map(date -> {
                            context.setDate(date);
                            // 模拟天气查询
                            String weatherInfo = String.format("在 %s, %s 的天气是：晴朗，温度 25℃。",
                                    context.getCity(),
                                    context.getDate());
                            context.setResult(weatherInfo);
                            return context;
                        });
            }


        }
        return Mono.just(context);
    }
} 