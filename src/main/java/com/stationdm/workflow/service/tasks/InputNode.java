package com.stationdm.workflow.service.tasks;

import com.stationdm.workflow.model.FlowContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@Order(1)
public class InputNode implements FlowNode {
    @Override
    public Mono<FlowContext> process(FlowContext context) {
        log.info("1. [InputNode] 接收到用户输入: {}", context.getUserInput());
        return Mono.just(context);
    }
} 