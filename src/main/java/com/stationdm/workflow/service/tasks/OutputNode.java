package com.stationdm.workflow.service.tasks;

import com.stationdm.workflow.model.FlowContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@Order(5)
public class OutputNode implements FlowNode {
    @Override
    public Mono<FlowContext> process(FlowContext context) {
        if (context.getResult() == null || context.getResult().isEmpty()) {
            context.setResult("抱歉，我无法处理您的请求。");
            log.info("6. [OutputNode] 流程结束，但未产生有效结果。");
        } else {
            log.info("6. [OutputNode] 流程成功结束。");
        }
        return Mono.just(context);
    }
} 