package com.stationdm.workflow.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class ChatNodeHandler {

    private final Map<String, Sinks.One<String>> pendingInputs = new ConcurrentHashMap<>();

    public Mono<String> waitForUserInput(String traceId, String question) {
        log.info("TraceID [{}] is asking: {}", traceId, question);
        Sinks.One<String> sink = Sinks.one();
        pendingInputs.put(traceId, sink);
        // 当订阅被取消时（例如，客户端断开连接），从Map中移除sink，防止内存泄漏
        return sink.asMono()
                .timeout(Duration.ofMinutes(2))
                .doFinally(signal -> pendingInputs.remove(traceId));
    }

    public void submitUserInput(String traceId, String userInput) {
        log.info("TraceID [{}] received answer: {}", traceId, userInput);
        Sinks.One<String> sink = pendingInputs.get(traceId);
        if (sink != null) {
            Sinks.EmitResult result = sink.tryEmitValue(userInput);
            if (!result.isSuccess()) {
                log.info("Failed to emit value for TraceID [{}]. Result: {}", traceId, result);
                // 如果注入失败（可能已经超时或取消），最好也将其移除
                pendingInputs.remove(traceId);
            }
        } else {
            log.info("No pending input found for TraceID [{}]", traceId);
        }
    }


}
