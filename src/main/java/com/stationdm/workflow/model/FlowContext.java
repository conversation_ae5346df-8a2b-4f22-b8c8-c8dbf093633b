package com.stationdm.workflow.model;

import lombok.Data;
import reactor.core.publisher.FluxSink;
import org.springframework.http.codec.ServerSentEvent;

@Data
public class FlowContext {
    private String traceId;
    private String userInput;
    private String intent;
    private String city;
    private String date;
    private String result;
    
    // 用于推送SSE消息的FluxSink
    private transient FluxSink<ServerSentEvent<String>> eventSink;
} 