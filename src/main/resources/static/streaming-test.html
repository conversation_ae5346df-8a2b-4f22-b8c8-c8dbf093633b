<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式返回测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 14px;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>流式返回测试页面</h1>
        <p>测试不同类型的Server-Sent Events流式返回效果</p>
    </div>

    <!-- 简单字符流式返回 -->
    <div class="test-section">
        <div class="test-title">1. 简单字符流式返回</div>
        <div class="test-description">逐字符返回，模拟打字机效果（100ms延迟）</div>
        <button class="button" onclick="testSimpleStreaming()">开始测试</button>
        <button class="button" onclick="clearOutput('simple-output')">清空</button>
        <div id="simple-status" class="status"></div>
        <div id="simple-output" class="output"></div>
    </div>

    <!-- 词汇流式返回 -->
    <div class="test-section">
        <div class="test-title">2. 词汇流式返回</div>
        <div class="test-description">按词汇返回，更接近真实AI响应（200ms延迟）</div>
        <button class="button" onclick="testWordStreaming()">开始测试</button>
        <button class="button" onclick="clearOutput('word-output')">清空</button>
        <div id="word-status" class="status"></div>
        <div id="word-output" class="output"></div>
    </div>

    <!-- AI对话流式返回 -->
    <div class="test-section">
        <div class="test-title">3. AI对话流式返回</div>
        <div class="test-description">模拟AI对话响应（50ms延迟）</div>
        <div class="input-group">
            <input type="text" id="chat-input" placeholder="输入你的问题..." value="什么是人工智能？">
            <button class="button" onclick="testChatStreaming()">发送</button>
            <button class="button" onclick="clearOutput('chat-output')">清空</button>
        </div>
        <div id="chat-status" class="status"></div>
        <div id="chat-output" class="output"></div>
    </div>

    <!-- 快速流式返回 -->
    <div class="test-section">
        <div class="test-title">4. 快速流式返回</div>
        <div class="test-description">无人为延迟，展示真实网络传输速度</div>
        <button class="button" onclick="testFastStreaming()">开始测试</button>
        <button class="button" onclick="clearOutput('fast-output')">清空</button>
        <div id="fast-status" class="status"></div>
        <div id="fast-output" class="output"></div>
    </div>

    <!-- 分块流式返回 -->
    <div class="test-section">
        <div class="test-title">5. 分块流式返回</div>
        <div class="test-description">按内容块返回，模拟真实AI模型的token生成（300ms延迟）</div>
        <button class="button" onclick="testChunkStreaming()">开始测试</button>
        <button class="button" onclick="clearOutput('chunk-output')">清空</button>
        <div id="chunk-status" class="status"></div>
        <div id="chunk-output" class="output"></div>
    </div>

    <script>
        function testSimpleStreaming() {
            streamTest('/api/streaming/simple', 'simple-output', 'simple-status');
        }

        function testWordStreaming() {
            streamTest('/api/streaming/words', 'word-output', 'word-status');
        }

        function testChatStreaming() {
            const input = document.getElementById('chat-input').value;
            const url = `/api/streaming/chat?input=${encodeURIComponent(input)}`;
            streamTest(url, 'chat-output', 'chat-status');
        }

        function testFastStreaming() {
            streamTest('/api/streaming/fast', 'fast-output', 'fast-status');
        }

        function testChunkStreaming() {
            streamTest('/api/streaming/chunks', 'chunk-output', 'chunk-status');
        }

        function streamTest(url, outputId, statusId) {
            const output = document.getElementById(outputId);
            const status = document.getElementById(statusId);
            
            // 清空输出
            output.textContent = '';
            
            // 设置连接状态
            status.textContent = '正在连接...';
            status.className = 'status connecting';
            
            // 创建EventSource
            const eventSource = new EventSource(url);
            
            eventSource.onopen = function() {
                status.textContent = '已连接，正在接收数据...';
                status.className = 'status connected';
            };
            
            eventSource.onmessage = function(event) {
                const data = event.data;
                
                // 检查是否是结束标记
                if (data.includes('[COMPLETE]') || data.includes('[END]') || 
                    data.includes('[STREAM_END]') || data.includes('[CHUNKS_COMPLETE]')) {
                    status.textContent = '流式传输完成';
                    status.className = 'status connected';
                    eventSource.close();
                    return;
                }
                
                // 追加数据到输出区域
                output.textContent += data;
                
                // 自动滚动到底部
                output.scrollTop = output.scrollHeight;
            };
            
            eventSource.onerror = function(error) {
                status.textContent = '连接错误: ' + error;
                status.className = 'status error';
                eventSource.close();
            };
        }

        function clearOutput(outputId) {
            document.getElementById(outputId).textContent = '';
        }
    </script>
</body>
</html>
